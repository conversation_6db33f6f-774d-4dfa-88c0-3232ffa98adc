<template>
    <div class="mobile_international" >
        <el-select v-model="tempCode" filterable  @click.native="openCountryList" @change="selectCountry">
            <el-option

              v-for="item in countryList"
              :key="item.EN"
              :label="'+'+item.value"
              :value="item.value">
            </el-option>
        </el-select>
        <el-input class="mobile_field" @input="inputMobile" v-model="tempMobile" :placeholder="lang.register_mobile" maxlength="11" autocomplete="new-password">
            <!-- <template slot="prepend">
                <div class="area_code" @click="openCountryList">+{{tempCode}}</div>
            </template> -->
        </el-input>

    </div>
</template>
<script>
import service from '../service/service'
import base from '../lib/base'
export default {
    mixins: [base],
    name: 'mobile_international',
    components: {},
    props:['form', 'mobile', 'nationalCode'],
    data(){
        return {
            dialogVisable: false,
            keyword: '',
            countryList: [],
            filterList: [],
            filterTimer: null,
            tempCode:'',
            tempMobile:''
        }
    },
    computed:{
        // getSmsVerificationCodeBtnContent(){
        //     let content = this.lang.forget_password_getcode;
        //     if (0 < this.getting_sms_verification_code_time) {
        //         content = this.getting_sms_verification_code_time + this.lang.forget_password_retry_query_sms_identification;
        //     }
        //     return content;
        // },
    },
    mounted(){
        this.tempCode = window.localStorage.getItem('MobileCountryCode')||'86'
        this.tempMobile=this.mobile;
        this.$emit('update:nationalCode', this.tempCode)
        this.version=window.localStorage.getItem('countryListVersion')
        this.openCountryList()
    },
    methods:{
        openCountryList() {
            const that = this
            this.dialogVisable = true

            this.countryList = window.localStorage.getItem("countryList")
            this.countryList = JSON.parse(this.countryList) || []
            this.filterList = this.countryList
            service.query_international_code_list({
                version:this.version
            }).then((res)=>{
                if (!res.error&&!res.data.error_code) {
                    this.filterList = this.countryList
                    if (res.data.version==this.version) {
                        //版本没更新，什么都不做
                        return ;
                    }
                    this.countryList=[]
                    for(let item of res.data.list){
                        this.countryList.push({
                            CN:item[1],
                            EN:item[0],
                            Abbreviation:item[2],
                            value:item[3]
                        })
                    }
                    this.version=res.data.version
                    window.localStorage.setItem('countryList',JSON.stringify(this.countryList))
                    window.localStorage.setItem('countryListVersion',res.data.version)
                }else{
                    that.$message.error(this.lang.get_country_list_fail)
                }
            }).catch(()=>{
                that.$message.error(this.lang.get_country_list_fail)
            })
        },
        selectCountry(value) {
            this.tempCode = value
            // 传递事件
            this.$emit('update:nationalCode', this.tempCode)
            window.localStorage.setItem('MobileCountryCode',this.tempCode)
        },
        // handleClose() {
        //     this.keyword = ''
        //     this.dialogVisable = false
        // },
        inputMobile(newVal) {
            this.$emit('update:mobile', newVal);
        },
        validate() {
            var regexp = /^[0-9]+$/;
            let result={
                pass:false,
                tip:this.lang.mobile_number_is_invalid_input_again
            }
            if(this.tempCode == '86') {
                regexp = /^1[0-9]{10}$/;
                result.tip=this.lang.mobile_number_is_invalid_input_again_cn
            }
            if (!regexp.test(this.mobile)){
                // this.$message.error(tip);
                return result
            } else {
                result.pass=true;
                return result
            }
        }
    }
}

</script>

<style lang="scss">
.mobile_international{
    position: relative;
    .mobile_field {
        // margin-top: 20px;
        input {
            margin-top: 0!important;
            position:relative;
            padding-left: 86px;
            &::after{
                position: absolute;
                content: '';
                width: 5rem;
                height: 100%;
                background: red;
                left: 0;
                top: 0;
            }
        }

    }
    .el-select{
        position: absolute;
        left: 0;
        top: 0;
        z-index: 2;
        width: 85px;
        .el-input__icon{
            line-height: 1;
        }
        .el-input.is-focus .el-input__inner{
            border:none;
        }
    }
    .el-form-item,.mobile_field{
        .el-input-group__prepend {
            position:relative;
            cursor:pointer;
            width: 3rem;
            .area_code {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                font-size: .8rem;
                text-align:center;
                line-height: 46px;
            }
        }
    }
}

</style>
